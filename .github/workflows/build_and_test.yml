#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

name: Build and test

on:
  workflow_call:
    inputs:
      java:
        required: false
        type: string
        default: 8
      branch:
        description: Branch to run the build against
        required: false
        type: string
        default: branch-3.4
      hadoop:
        description: Hadoop version to run with. HADOOP_PROFILE environment variable should accept it.
        required: false
        type: string
        default: hadoop3
      envs:
        description: Additional environment variables to set when running the tests. Should be in JSON format.
        required: false
        type: string
        default: '{}'
      jobs:
        description: >-
          Jobs to run, and should be in JSON format. The values should be matched with the job's key defined
          in this file, e.g., build. See precondition job below.
        required: false
        type: string
        default: ''
jobs:
  precondition:
    name: Check changes
    runs-on: ubuntu-20.04
    env:
      GITHUB_PREV_SHA: ${{ github.event.before }}
    outputs:
      required: ${{ steps.set-outputs.outputs.required }}
      image_url: >-
        ${{
          (inputs.branch == 'branch-3.4' && steps.infra-image-outputs.outputs.image_url)
          || 'dongjoon/apache-spark-github-action-image:20220207'
        }}
    steps:
    - name: Checkout Spark repository
      uses: actions/checkout@v3
      with:
        fetch-depth: 0
        repository: apache/spark
        ref: ${{ inputs.branch }}
    - name: Sync the current branch with the latest in Apache Spark
      if: github.repository != 'apache/spark'
      run: |
        echo "APACHE_SPARK_REF=$(git rev-parse HEAD)" >> $GITHUB_ENV
        git fetch https://github.com/$GITHUB_REPOSITORY.git ${GITHUB_REF#refs/heads/}
        git -c user.name='Apache Spark Test Account' -c user.email='<EMAIL>' merge --no-commit --progress --squash FETCH_HEAD
        git -c user.name='Apache Spark Test Account' -c user.email='<EMAIL>' commit -m "Merged commit" --allow-empty
    - name: Check all modules
      id: set-outputs
      run: |
        if [ -z "${{ inputs.jobs }}" ]; then
          # is-changed.py is missing in branch-3.2, and it might run in scheduled build, see also SPARK-39517
          pyspark=true; sparkr=true; tpcds=true; docker=true;
          if [ -f "./dev/is-changed.py" ]; then
            pyspark_modules=`cd dev && python -c "import sparktestsupport.modules as m; print(','.join(m.name for m in m.all_modules if m.name.startswith('pyspark')))"`
            pyspark=`./dev/is-changed.py -m $pyspark_modules`
          fi
          if [[ "${{ github.repository }}" != 'apache/spark' ]]; then
            pandas=$pyspark
            kubernetes=`./dev/is-changed.py -m kubernetes`
            sparkr=`./dev/is-changed.py -m sparkr`
            tpcds=`./dev/is-changed.py -m sql`
            docker=`./dev/is-changed.py -m docker-integration-tests`
          else
            pandas=false
            kubernetes=false
            sparkr=false
            tpcds=false
            docker=false
          fi
          build=`./dev/is-changed.py -m "core,unsafe,kvstore,avro,network-common,network-shuffle,repl,launcher,examples,sketch,graphx,catalyst,hive-thriftserver,streaming,sql-kafka-0-10,streaming-kafka-0-10,mllib-local,mllib,yarn,mesos,kubernetes,hadoop-cloud,spark-ganglia-lgpl,connect,protobuf"`
          precondition="
            {
              \"build\": \"$build\",
              \"pyspark\": \"$pyspark\",
              \"pyspark-pandas\": \"$pandas\",
              \"sparkr\": \"$sparkr\",
              \"tpcds-1g\": \"$tpcds\",
              \"docker-integration-tests\": \"$docker\",
              \"scala-213\": \"$build\",
              \"java-11-17\": \"$build\",
              \"lint\" : \"true\",
              \"k8s-integration-tests\" : \"$kubernetes\",
            }"
          echo $precondition # For debugging
          # Remove `\n` to avoid "Invalid format" error
          precondition="${precondition//$'\n'/}}"
          echo "required=$precondition" >> $GITHUB_OUTPUT
        else
          # This is usually set by scheduled jobs.
          precondition='${{ inputs.jobs }}'
          echo $precondition # For debugging
          precondition="${precondition//$'\n'/}"
          echo "required=$precondition" >> $GITHUB_OUTPUT
        fi
    - name: Generate infra image URL
      id: infra-image-outputs
      run: |
        # Convert to lowercase to meet Docker repo name requirement
        REPO_OWNER=$(echo "${{ github.repository_owner }}" | tr '[:upper:]' '[:lower:]')
        IMG_NAME="apache-spark-ci-image:${{ inputs.branch }}-${{ github.run_id }}"
        IMG_URL="ghcr.io/$REPO_OWNER/$IMG_NAME"
        echo "image_url=$IMG_URL" >> $GITHUB_OUTPUT

  # Build: build Spark and run the tests for specified modules.
  build:
    name: "Build modules: ${{ matrix.modules }} ${{ matrix.comment }}"
    needs: precondition
    if: fromJson(needs.precondition.outputs.required).build == 'true'
    # Ubuntu 20.04 is the latest LTS. The next LTS is 22.04.
    runs-on: ubuntu-20.04
    strategy:
      fail-fast: false
      matrix:
        java:
          - ${{ inputs.java }}
        hadoop:
          - ${{ inputs.hadoop }}
        hive:
          - hive2.3
        # TODO(SPARK-32246): We don't test 'streaming-kinesis-asl' for now.
        # Kinesis tests depends on external Amazon kinesis service.
        # Note that the modules below are from sparktestsupport/modules.py.
        modules:
          - >-
            core, unsafe, kvstore, avro,
            network-common, network-shuffle, repl, launcher,
            examples, sketch, graphx
          - >-
            catalyst, hive-thriftserver
          - >-
            streaming, sql-kafka-0-10, streaming-kafka-0-10,
            mllib-local, mllib,
            yarn, mesos, kubernetes, hadoop-cloud, spark-ganglia-lgpl,
            connect, protobuf
        # Here, we split Hive and SQL tests into some of slow ones and the rest of them.
        included-tags: [""]
        excluded-tags: [""]
        comment: [""]
        include:
          # Hive tests
          - modules: hive
            java: ${{ inputs.java }}
            hadoop: ${{ inputs.hadoop }}
            hive: hive2.3
            included-tags: org.apache.spark.tags.SlowHiveTest
            comment: "- slow tests"
          - modules: hive
            java: ${{ inputs.java }}
            hadoop: ${{ inputs.hadoop }}
            hive: hive2.3
            excluded-tags: org.apache.spark.tags.SlowHiveTest
            comment: "- other tests"
          # SQL tests
          - modules: sql
            java: ${{ inputs.java }}
            hadoop: ${{ inputs.hadoop }}
            hive: hive2.3
            included-tags: org.apache.spark.tags.ExtendedSQLTest
            comment: "- extended tests"
          - modules: sql
            java: ${{ inputs.java }}
            hadoop: ${{ inputs.hadoop }}
            hive: hive2.3
            included-tags: org.apache.spark.tags.SlowSQLTest
            comment: "- slow tests"
          - modules: sql
            java: ${{ inputs.java }}
            hadoop: ${{ inputs.hadoop }}
            hive: hive2.3
            excluded-tags: org.apache.spark.tags.ExtendedSQLTest,org.apache.spark.tags.SlowSQLTest
            comment: "- other tests"
    env:
      MODULES_TO_TEST: ${{ matrix.modules }}
      EXCLUDED_TAGS: ${{ matrix.excluded-tags }}
      INCLUDED_TAGS: ${{ matrix.included-tags }}
      HADOOP_PROFILE: ${{ matrix.hadoop }}
      HIVE_PROFILE: ${{ matrix.hive }}
      GITHUB_PREV_SHA: ${{ github.event.before }}
      SPARK_LOCAL_IP: localhost
      SKIP_UNIDOC: true
      SKIP_MIMA: true
      SKIP_PACKAGING: true
    steps:
    - name: Checkout Spark repository
      uses: actions/checkout@v3
      # In order to fetch changed files
      with:
        fetch-depth: 0
        repository: apache/spark
        ref: ${{ inputs.branch }}
    - name: Sync the current branch with the latest in Apache Spark
      if: github.repository != 'apache/spark'
      run: |
        echo "APACHE_SPARK_REF=$(git rev-parse HEAD)" >> $GITHUB_ENV
        git fetch https://github.com/$GITHUB_REPOSITORY.git ${GITHUB_REF#refs/heads/}
        git -c user.name='Apache Spark Test Account' -c user.email='<EMAIL>' merge --no-commit --progress --squash FETCH_HEAD
        git -c user.name='Apache Spark Test Account' -c user.email='<EMAIL>' commit -m "Merged commit" --allow-empty
    # Cache local repositories. Note that GitHub Actions cache has a 2G limit.
    - name: Cache Scala, SBT and Maven
      uses: actions/cache@v3
      with:
        path: |
          build/apache-maven-*
          build/scala-*
          build/*.jar
          ~/.sbt
        key: build-${{ hashFiles('**/pom.xml', 'project/build.properties', 'build/mvn', 'build/sbt', 'build/sbt-launch-lib.bash', 'build/spark-build-info') }}
        restore-keys: |
          build-
    - name: Cache Coursier local repository
      uses: actions/cache@v3
      with:
        path: ~/.cache/coursier
        key: ${{ matrix.java }}-${{ matrix.hadoop }}-coursier-${{ hashFiles('**/pom.xml', '**/plugins.sbt') }}
        restore-keys: |
          ${{ matrix.java }}-${{ matrix.hadoop }}-coursier-
    - name: Install Java ${{ matrix.java }}
      uses: actions/setup-java@v3
      with:
        distribution: temurin
        java-version: ${{ matrix.java }}
    - name: Install Python 3.8
      uses: actions/setup-python@v4
      # We should install one Python that is higher then 3+ for SQL and Yarn because:
      # - SQL component also has Python related tests, for example, IntegratedUDFTestUtils.
      # - Yarn has a Python specific test too, for example, YarnClusterSuite.
      if: contains(matrix.modules, 'yarn') || (contains(matrix.modules, 'sql') && !contains(matrix.modules, 'sql-'))
      with:
        python-version: 3.8
        architecture: x64
    - name: Install Python packages (Python 3.8)
      if: (contains(matrix.modules, 'sql') && !contains(matrix.modules, 'sql-'))
      run: |
        python3.8 -m pip install 'numpy>=1.20.0' 'pyarrow==12.0.1' pandas scipy unittest-xml-reporting 'grpcio==1.48.1' 'protobuf==3.19.5'
        python3.8 -m pip list
    # Run the tests.
    - name: Run tests
      env: ${{ fromJSON(inputs.envs) }}
      run: |
        # Hive "other tests" test needs larger metaspace size based on experiment.
        if [[ "$MODULES_TO_TEST" == "hive" ]] && [[ "$EXCLUDED_TAGS" == "org.apache.spark.tags.SlowHiveTest" ]]; then export METASPACE_SIZE=2g; fi
        export SERIAL_SBT_TESTS=1
        ./dev/run-tests --parallelism 1 --modules "$MODULES_TO_TEST" --included-tags "$INCLUDED_TAGS" --excluded-tags "$EXCLUDED_TAGS"
    - name: Upload test results to report
      if: always()
      uses: actions/upload-artifact@v3
      with:
        name: test-results-${{ matrix.modules }}-${{ matrix.comment }}-${{ matrix.java }}-${{ matrix.hadoop }}-${{ matrix.hive }}
        path: "**/target/test-reports/*.xml"
    - name: Upload unit tests log files
      if: failure()
      uses: actions/upload-artifact@v3
      with:
        name: unit-tests-log-${{ matrix.modules }}-${{ matrix.comment }}-${{ matrix.java }}-${{ matrix.hadoop }}-${{ matrix.hive }}
        path: "**/target/unit-tests.log"

  infra-image:
    name: "Base image build"
    needs: precondition
    # Currently, only enable docker build from cache for `master` branch jobs
    if: >-
      (fromJson(needs.precondition.outputs.required).pyspark == 'true' ||
      fromJson(needs.precondition.outputs.required).lint == 'true' ||
      fromJson(needs.precondition.outputs.required).sparkr == 'true') &&
      inputs.branch == 'branch-3.4'
    runs-on: ubuntu-latest
    permissions:
      packages: write
    steps:
      - name: Login to GitHub Container Registry
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Checkout Spark repository
        uses: actions/checkout@v3
        # In order to fetch changed files
        with:
          fetch-depth: 0
          repository: apache/spark
          ref: ${{ inputs.branch }}
      - name: Sync the current branch with the latest in Apache Spark
        if: github.repository != 'apache/spark'
        run: |
          echo "APACHE_SPARK_REF=$(git rev-parse HEAD)" >> $GITHUB_ENV
          git fetch https://github.com/$GITHUB_REPOSITORY.git ${GITHUB_REF#refs/heads/}
          git -c user.name='Apache Spark Test Account' -c user.email='<EMAIL>' merge --no-commit --progress --squash FETCH_HEAD
          git -c user.name='Apache Spark Test Account' -c user.email='<EMAIL>' commit -m "Merged commit" --allow-empty
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      - name: Build and push
        id: docker_build
        uses: docker/build-push-action@v3
        with:
          context: ./dev/infra/
          push: true
          tags: |
            ${{ needs.precondition.outputs.image_url }}
          # Use the infra image cache to speed up
          cache-from: type=registry,ref=ghcr.io/apache/spark/apache-spark-github-action-image-cache:${{ inputs.branch }}

  pyspark:
    needs: [precondition, infra-image]
    # always run if pyspark == 'true', even infra-image is skip (such as non-master job)
    if: always() && fromJson(needs.precondition.outputs.required).pyspark == 'true'
    name: "Build modules: ${{ matrix.modules }}"
    runs-on: ubuntu-20.04
    container:
      image: ${{ needs.precondition.outputs.image_url }}
    strategy:
      fail-fast: false
      matrix:
        java:
          - ${{ inputs.java }}
        modules:
          - >-
            pyspark-errors
          - >-
            pyspark-sql, pyspark-mllib, pyspark-resource
          - >-
            pyspark-core, pyspark-streaming, pyspark-ml
          - >-
            pyspark-pandas
          - >-
            pyspark-pandas-slow
          - >-
            pyspark-connect
        exclude:
          # Always run if pyspark-pandas == 'true', even infra-image is skip (such as non-master job)
          # In practice, the build will run in individual PR, but not against the individual commit
          # in Apache Spark repository.
          - modules: ${{ fromJson(needs.precondition.outputs.required).pyspark-pandas != 'true' && 'pyspark-pandas' }}
          - modules: ${{ fromJson(needs.precondition.outputs.required).pyspark-pandas != 'true' && 'pyspark-pandas-slow' }}
    env:
      MODULES_TO_TEST: ${{ matrix.modules }}
      HADOOP_PROFILE: ${{ inputs.hadoop }}
      HIVE_PROFILE: hive2.3
      GITHUB_PREV_SHA: ${{ github.event.before }}
      SPARK_LOCAL_IP: localhost
      SKIP_UNIDOC: true
      SKIP_MIMA: true
      SKIP_PACKAGING: true
      METASPACE_SIZE: 1g
    steps:
    - name: Checkout Spark repository
      uses: actions/checkout@v3
      # In order to fetch changed files
      with:
        fetch-depth: 0
        repository: apache/spark
        ref: ${{ inputs.branch }}
    - name: Add GITHUB_WORKSPACE to git trust safe.directory
      run: |
        git config --global --add safe.directory ${GITHUB_WORKSPACE}
    - name: Sync the current branch with the latest in Apache Spark
      if: github.repository != 'apache/spark'
      run: |
        echo "APACHE_SPARK_REF=$(git rev-parse HEAD)" >> $GITHUB_ENV
        git fetch https://github.com/$GITHUB_REPOSITORY.git ${GITHUB_REF#refs/heads/}
        git -c user.name='Apache Spark Test Account' -c user.email='<EMAIL>' merge --no-commit --progress --squash FETCH_HEAD
        git -c user.name='Apache Spark Test Account' -c user.email='<EMAIL>' commit -m "Merged commit" --allow-empty
    # Cache local repositories. Note that GitHub Actions cache has a 2G limit.
    - name: Cache Scala, SBT and Maven
      uses: actions/cache@v3
      with:
        path: |
          build/apache-maven-*
          build/scala-*
          build/*.jar
          ~/.sbt
        key: build-${{ hashFiles('**/pom.xml', 'project/build.properties', 'build/mvn', 'build/sbt', 'build/sbt-launch-lib.bash', 'build/spark-build-info') }}
        restore-keys: |
          build-
    - name: Cache Coursier local repository
      uses: actions/cache@v3
      with:
        path: ~/.cache/coursier
        key: pyspark-coursier-${{ hashFiles('**/pom.xml', '**/plugins.sbt') }}
        restore-keys: |
          pyspark-coursier-
    - name: Free up disk space
      shell: 'script -q -e -c "bash {0}"'
      run: |
        if [ -f ./dev/free_disk_space_container ]; then
          ./dev/free_disk_space_container
        fi
    - name: Install Java ${{ matrix.java }}
      uses: actions/setup-java@v3
      with:
        distribution: temurin
        java-version: ${{ matrix.java }}
    - name: List Python packages (Python 3.9, PyPy3)
      run: |
        python3.9 -m pip list
        pypy3 -m pip list
    - name: Install Conda for pip packaging test
      if: ${{ matrix.modules == 'pyspark-errors' }}
      run: |
        curl -s https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh > miniconda.sh
        bash miniconda.sh -b -p $HOME/miniconda
    # Run the tests.
    - name: Run tests
      env: ${{ fromJSON(inputs.envs) }}
      shell: 'script -q -e -c "bash {0}"'
      run: |
        if [[ "$MODULES_TO_TEST" == "pyspark-errors" ]]; then
          export PATH=$PATH:$HOME/miniconda/bin
          export SKIP_PACKAGING=false
          echo "Python Packaging Tests Enabled!"
        fi
        ./dev/run-tests --parallelism 1 --modules "$MODULES_TO_TEST"
    - name: Upload coverage to Codecov
      if: fromJSON(inputs.envs).PYSPARK_CODECOV == 'true'
      uses: codecov/codecov-action@v2
      with:
        files: ./python/coverage.xml
        flags: unittests
        name: PySpark
    - name: Upload test results to report
      if: always()
      uses: actions/upload-artifact@v3
      with:
        name: test-results-${{ matrix.modules }}--8-${{ inputs.hadoop }}-hive2.3
        path: "**/target/test-reports/*.xml"
    - name: Upload unit tests log files
      if: failure()
      uses: actions/upload-artifact@v3
      with:
        name: unit-tests-log-${{ matrix.modules }}--8-${{ inputs.hadoop }}-hive2.3
        path: "**/target/unit-tests.log"

  sparkr:
    needs: [precondition, infra-image]
    # always run if sparkr == 'true', even infra-image is skip (such as non-master job)
    if: always() && fromJson(needs.precondition.outputs.required).sparkr == 'true'
    name: "Build modules: sparkr"
    runs-on: ubuntu-20.04
    container:
      image: ${{ needs.precondition.outputs.image_url }}
    env:
      HADOOP_PROFILE: ${{ inputs.hadoop }}
      HIVE_PROFILE: hive2.3
      GITHUB_PREV_SHA: ${{ github.event.before }}
      SPARK_LOCAL_IP: localhost
      SKIP_MIMA: true
      SKIP_PACKAGING: true
    steps:
    - name: Checkout Spark repository
      uses: actions/checkout@v3
      # In order to fetch changed files
      with:
        fetch-depth: 0
        repository: apache/spark
        ref: ${{ inputs.branch }}
    - name: Add GITHUB_WORKSPACE to git trust safe.directory
      run: |
        git config --global --add safe.directory ${GITHUB_WORKSPACE}
    - name: Sync the current branch with the latest in Apache Spark
      if: github.repository != 'apache/spark'
      run: |
        echo "APACHE_SPARK_REF=$(git rev-parse HEAD)" >> $GITHUB_ENV
        git fetch https://github.com/$GITHUB_REPOSITORY.git ${GITHUB_REF#refs/heads/}
        git -c user.name='Apache Spark Test Account' -c user.email='<EMAIL>' merge --no-commit --progress --squash FETCH_HEAD
        git -c user.name='Apache Spark Test Account' -c user.email='<EMAIL>' commit -m "Merged commit" --allow-empty
    # Cache local repositories. Note that GitHub Actions cache has a 2G limit.
    - name: Cache Scala, SBT and Maven
      uses: actions/cache@v3
      with:
        path: |
          build/apache-maven-*
          build/scala-*
          build/*.jar
          ~/.sbt
        key: build-${{ hashFiles('**/pom.xml', 'project/build.properties', 'build/mvn', 'build/sbt', 'build/sbt-launch-lib.bash', 'build/spark-build-info') }}
        restore-keys: |
          build-
    - name: Cache Coursier local repository
      uses: actions/cache@v3
      with:
        path: ~/.cache/coursier
        key: sparkr-coursier-${{ hashFiles('**/pom.xml', '**/plugins.sbt') }}
        restore-keys: |
          sparkr-coursier-
    - name: Free up disk space
      shell: 'script -q -e -c "bash {0}"'
      run: |
        if [ -f ./dev/free_disk_space_container ]; then
          ./dev/free_disk_space_container
        fi
    - name: Install Java ${{ inputs.java }}
      uses: actions/setup-java@v3
      with:
        distribution: temurin
        java-version: ${{ inputs.java }}
    - name: Run tests
      env: ${{ fromJSON(inputs.envs) }}
      run: |
        # The followings are also used by `r-lib/actions/setup-r` to avoid
        # R issues at docker environment
        export TZ=UTC
        export _R_CHECK_SYSTEM_CLOCK_=FALSE
        ./dev/run-tests --parallelism 1 --modules sparkr
    - name: Upload test results to report
      if: always()
      uses: actions/upload-artifact@v3
      with:
        name: test-results-sparkr--8-${{ inputs.hadoop }}-hive2.3
        path: "**/target/test-reports/*.xml"

  # Static analysis, and documentation build
  lint:
    needs: [precondition, infra-image]
    # always run if lint == 'true', even infra-image is skip (such as non-master job)
    if: always() && fromJson(needs.precondition.outputs.required).lint == 'true'
    name: Linters, licenses, dependencies and documentation generation
    runs-on: ubuntu-20.04
    env:
      LC_ALL: C.UTF-8
      LANG: C.UTF-8
      PYSPARK_DRIVER_PYTHON: python3.9
      PYSPARK_PYTHON: python3.9
      GITHUB_PREV_SHA: ${{ github.event.before }}
    container:
      image: ${{ needs.precondition.outputs.image_url }}
    steps:
    - name: Checkout Spark repository
      uses: actions/checkout@v3
      with:
        fetch-depth: 0
        repository: apache/spark
        ref: ${{ inputs.branch }}
    - name: Add GITHUB_WORKSPACE to git trust safe.directory
      run: |
        git config --global --add safe.directory ${GITHUB_WORKSPACE}
    - name: Sync the current branch with the latest in Apache Spark
      if: github.repository != 'apache/spark'
      run: |
        echo "APACHE_SPARK_REF=$(git rev-parse HEAD)" >> $GITHUB_ENV
        git fetch https://github.com/$GITHUB_REPOSITORY.git ${GITHUB_REF#refs/heads/}
        git -c user.name='Apache Spark Test Account' -c user.email='<EMAIL>' merge --no-commit --progress --squash FETCH_HEAD
        git -c user.name='Apache Spark Test Account' -c user.email='<EMAIL>' commit -m "Merged commit" --allow-empty
    # Cache local repositories. Note that GitHub Actions cache has a 2G limit.
    - name: Cache Scala, SBT and Maven
      uses: actions/cache@v3
      with:
        path: |
          build/apache-maven-*
          build/scala-*
          build/*.jar
          ~/.sbt
        key: build-${{ hashFiles('**/pom.xml', 'project/build.properties', 'build/mvn', 'build/sbt', 'build/sbt-launch-lib.bash', 'build/spark-build-info') }}
        restore-keys: |
          build-
    - name: Cache Coursier local repository
      uses: actions/cache@v3
      with:
        path: ~/.cache/coursier
        key: docs-coursier-${{ hashFiles('**/pom.xml', '**/plugins.sbt') }}
        restore-keys: |
          docs-coursier-
    - name: Cache Maven local repository
      uses: actions/cache@v3
      with:
        path: ~/.m2/repository
        key: docs-maven-${{ hashFiles('**/pom.xml') }}
        restore-keys: |
          docs-maven-
    - name: Free up disk space
      run: |
        if [ -f ./dev/free_disk_space_container ]; then
          ./dev/free_disk_space_container
        fi
    - name: Install Java 8
      uses: actions/setup-java@v3
      with:
        distribution: temurin
        java-version: 8
    - name: License test
      run: ./dev/check-license
    - name: Dependencies test
      run: ./dev/test-dependencies.sh
    - name: MIMA test
      run: ./dev/mima
    - name: Scala linter
      run: ./dev/lint-scala
    - name: Java linter
      run: ./dev/lint-java
    - name: Spark connect jvm client mima check
      if: inputs.branch != 'branch-3.2' && inputs.branch != 'branch-3.3'
      run: ./dev/connect-jvm-client-mima-check
    - name: Install Python linter dependencies
      run: |
        # TODO(SPARK-32407): Sphinx 3.1+ does not correctly index nested classes.
        #   See also https://github.com/sphinx-doc/sphinx/issues/7551.
        # Jinja2 3.0.0+ causes error when building with Sphinx.
        #   See also https://issues.apache.org/jira/browse/SPARK-35375.
        python3.9 -m pip install 'flake8==3.9.0' pydata_sphinx_theme 'mypy==0.920' 'pytest==7.1.3' 'pytest-mypy-plugins==1.9.3' numpydoc 'jinja2<3.0.0' 'black==22.6.0'
        python3.9 -m pip install 'pandas-stubs==********' ipython 'grpcio==1.48.1' 'grpc-stubs==1.24.11' 'googleapis-common-protos-stubs==2.2.0'
    - name: Python linter
      run: PYTHON_EXECUTABLE=python3.9 ./dev/lint-python
    - name: Install JavaScript linter dependencies
      run: |
        apt update
        apt-get install -y nodejs npm
    - name: JS linter
      run: ./dev/lint-js
    - name: Install R linter dependencies and SparkR
      run: |
        apt update
        apt-get install -y libcurl4-openssl-dev libgit2-dev libssl-dev libxml2-dev \
          libfontconfig1-dev libharfbuzz-dev libfribidi-dev libfreetype6-dev libpng-dev \
          libtiff5-dev libjpeg-dev
        Rscript -e "install.packages(c('devtools'), repos='https://cloud.r-project.org/')"
        Rscript -e "devtools::install_version('lintr', version='2.0.1', repos='https://cloud.r-project.org')"
        ./R/install-dev.sh
    - name: Install dependencies for documentation generation
      run: |
        # pandoc is required to generate PySpark APIs as well in nbsphinx.
        apt-get install -y libcurl4-openssl-dev pandoc
        # TODO(SPARK-32407): Sphinx 3.1+ does not correctly index nested classes.
        #   See also https://github.com/sphinx-doc/sphinx/issues/7551.
        # Jinja2 3.0.0+ causes error when building with Sphinx.
        #   See also https://issues.apache.org/jira/browse/SPARK-35375.
        # Pin the MarkupSafe to 2.0.1 to resolve the CI error.
        #   See also https://issues.apache.org/jira/browse/SPARK-38279.
        python3.9 -m pip install 'sphinx<3.1.0' mkdocs pydata_sphinx_theme 'sphinx-copybutton==0.5.2' nbsphinx numpydoc 'jinja2<3.0.0' 'markupsafe==2.0.1' 'pyzmq<24.0.0' 'sphinxcontrib-applehelp==1.0.4' 'sphinxcontrib-devhelp==1.0.2' 'sphinxcontrib-htmlhelp==2.0.1' 'sphinxcontrib-qthelp==1.0.3' 'sphinxcontrib-serializinghtml==1.1.5' 'nest-asyncio==1.5.8' 'rpds-py==0.16.2' 'alabaster==0.7.13'
        python3.9 -m pip install ipython_genutils # See SPARK-38517
        python3.9 -m pip install sphinx_plotly_directive 'numpy>=1.20.0' 'pyarrow==12.0.1' pandas 'plotly>=4.8'
        python3.9 -m pip install 'nbsphinx==0.9.3'
        python3.9 -m pip install 'docutils<0.18.0' # See SPARK-39421
        apt-get update -y
        apt-get install -y ruby ruby-dev
        Rscript -e "install.packages(c('devtools', 'testthat', 'knitr', 'rmarkdown', 'markdown', 'e1071', 'roxygen2', 'ggplot2', 'mvtnorm', 'statmod'), repos='https://cloud.r-project.org/')"
        Rscript -e "devtools::install_version('pkgdown', version='2.0.1', repos='https://cloud.r-project.org')"
        Rscript -e "devtools::install_version('preferably', version='0.4', repos='https://cloud.r-project.org')"
        gem install bundler -v 2.4.22
        cd docs
        bundle install
    - name: R linter
      run: ./dev/lint-r
    - name: Run documentation build
      run: |
        if [ -f "./dev/is-changed.py" ]; then
          # Skip PySpark and SparkR docs while keeping Scala/Java/SQL docs
          pyspark_modules=`cd dev && python3.9 -c "import sparktestsupport.modules as m; print(','.join(m.name for m in m.all_modules if m.name.startswith('pyspark')))"`
          if [ `./dev/is-changed.py -m $pyspark_modules` = false ]; then export SKIP_PYTHONDOC=1; fi
          if [ `./dev/is-changed.py -m sparkr` = false ]; then export SKIP_RDOC=1; fi
        fi
        cd docs
        bundle exec jekyll build

  java-11-17:
    needs: precondition
    if: fromJson(needs.precondition.outputs.required).java-11-17 == 'true'
    name: Java ${{ matrix.java }} build with Maven
    strategy:
      fail-fast: false
      matrix:
        java:
          - 11
          - 17
    runs-on: ubuntu-20.04
    steps:
    - name: Checkout Spark repository
      uses: actions/checkout@v3
      with:
        fetch-depth: 0
        repository: apache/spark
        ref: ${{ inputs.branch }}
    - name: Sync the current branch with the latest in Apache Spark
      if: github.repository != 'apache/spark'
      run: |
        git fetch https://github.com/$GITHUB_REPOSITORY.git ${GITHUB_REF#refs/heads/}
        git -c user.name='Apache Spark Test Account' -c user.email='<EMAIL>' merge --no-commit --progress --squash FETCH_HEAD
        git -c user.name='Apache Spark Test Account' -c user.email='<EMAIL>' commit -m "Merged commit" --allow-empty
    - name: Cache Scala, SBT and Maven
      uses: actions/cache@v3
      with:
        path: |
          build/apache-maven-*
          build/scala-*
          build/*.jar
          ~/.sbt
        key: build-${{ hashFiles('**/pom.xml', 'project/build.properties', 'build/mvn', 'build/sbt', 'build/sbt-launch-lib.bash', 'build/spark-build-info') }}
        restore-keys: |
          build-
    - name: Cache Maven local repository
      uses: actions/cache@v3
      with:
        path: ~/.m2/repository
        key: java${{ matrix.java }}-maven-${{ hashFiles('**/pom.xml') }}
        restore-keys: |
          java${{ matrix.java }}-maven-
    - name: Install Java ${{ matrix.java }}
      uses: actions/setup-java@v3
      with:
        distribution: temurin
        java-version: ${{ matrix.java }}
    - name: Build with Maven
      run: |
        export MAVEN_OPTS="-Xss64m -Xmx2g -XX:ReservedCodeCacheSize=1g -Dorg.slf4j.simpleLogger.defaultLogLevel=WARN"
        export MAVEN_CLI_OPTS="--no-transfer-progress"
        export JAVA_VERSION=${{ matrix.java }}
        # It uses Maven's 'install' intentionally, see https://github.com/apache/spark/pull/26414.
        ./build/mvn $MAVEN_CLI_OPTS -DskipTests -Pyarn -Pmesos -Pkubernetes -Pvolcano -Phive -Phive-thriftserver -Phadoop-cloud -Djava.version=${JAVA_VERSION/-ea} install
        rm -rf ~/.m2/repository/org/apache/spark

  scala-213:
    needs: precondition
    if: fromJson(needs.precondition.outputs.required).scala-213 == 'true'
    name: Scala 2.13 build with SBT
    runs-on: ubuntu-20.04
    steps:
    - name: Checkout Spark repository
      uses: actions/checkout@v3
      with:
        fetch-depth: 0
        repository: apache/spark
        ref: ${{ inputs.branch }}
    - name: Sync the current branch with the latest in Apache Spark
      if: github.repository != 'apache/spark'
      run: |
        git fetch https://github.com/$GITHUB_REPOSITORY.git ${GITHUB_REF#refs/heads/}
        git -c user.name='Apache Spark Test Account' -c user.email='<EMAIL>' merge --no-commit --progress --squash FETCH_HEAD
        git -c user.name='Apache Spark Test Account' -c user.email='<EMAIL>' commit -m "Merged commit" --allow-empty
    - name: Cache Scala, SBT and Maven
      uses: actions/cache@v3
      with:
        path: |
          build/apache-maven-*
          build/scala-*
          build/*.jar
          ~/.sbt
        key: build-${{ hashFiles('**/pom.xml', 'project/build.properties', 'build/mvn', 'build/sbt', 'build/sbt-launch-lib.bash', 'build/spark-build-info') }}
        restore-keys: |
          build-
    - name: Cache Coursier local repository
      uses: actions/cache@v3
      with:
        path: ~/.cache/coursier
        key: scala-213-coursier-${{ hashFiles('**/pom.xml', '**/plugins.sbt') }}
        restore-keys: |
          scala-213-coursier-
    - name: Install Java 8
      uses: actions/setup-java@v3
      with:
        distribution: temurin
        java-version: 8
    - name: Build with SBT
      run: |
        ./dev/change-scala-version.sh 2.13
        ./build/sbt -Pyarn -Pmesos -Pkubernetes -Pvolcano -Phive -Phive-thriftserver -Phadoop-cloud -Pkinesis-asl -Pdocker-integration-tests -Pkubernetes-integration-tests -Pspark-ganglia-lgpl -Pscala-2.13 compile Test/compile

  # Any TPC-DS related updates on this job need to be applied to tpcds-1g-gen job of benchmark.yml as well
  tpcds-1g:
    needs: precondition
    if: fromJson(needs.precondition.outputs.required).tpcds-1g == 'true'
    name: Run TPC-DS queries with SF=1
    runs-on: ubuntu-20.04
    env:
      SPARK_LOCAL_IP: localhost
    steps:
    - name: Checkout Spark repository
      uses: actions/checkout@v3
      with:
        fetch-depth: 0
        repository: apache/spark
        ref: ${{ inputs.branch }}
    - name: Sync the current branch with the latest in Apache Spark
      if: github.repository != 'apache/spark'
      run: |
        git fetch https://github.com/$GITHUB_REPOSITORY.git ${GITHUB_REF#refs/heads/}
        git -c user.name='Apache Spark Test Account' -c user.email='<EMAIL>' merge --no-commit --progress --squash FETCH_HEAD
        git -c user.name='Apache Spark Test Account' -c user.email='<EMAIL>' commit -m "Merged commit" --allow-empty
    - name: Cache Scala, SBT and Maven
      uses: actions/cache@v3
      with:
        path: |
          build/apache-maven-*
          build/scala-*
          build/*.jar
          ~/.sbt
        key: build-${{ hashFiles('**/pom.xml', 'project/build.properties', 'build/mvn', 'build/sbt', 'build/sbt-launch-lib.bash', 'build/spark-build-info') }}
        restore-keys: |
          build-
    - name: Cache Coursier local repository
      uses: actions/cache@v3
      with:
        path: ~/.cache/coursier
        key: tpcds-coursier-${{ hashFiles('**/pom.xml', '**/plugins.sbt') }}
        restore-keys: |
          tpcds-coursier-
    - name: Install Java 8
      uses: actions/setup-java@v3
      with:
        distribution: temurin
        java-version: 8
    - name: Cache TPC-DS generated data
      id: cache-tpcds-sf-1
      uses: actions/cache@v3
      with:
        path: ./tpcds-sf-1
        key: tpcds-${{ hashFiles('.github/workflows/build_and_test.yml', 'sql/core/src/test/scala/org/apache/spark/sql/TPCDSSchema.scala') }}
    - name: Checkout tpcds-kit repository
      if: steps.cache-tpcds-sf-1.outputs.cache-hit != 'true'
      uses: actions/checkout@v3
      with:
        repository: databricks/tpcds-kit
        ref: 2a5078a782192ddb6efbcead8de9973d6ab4f069
        path: ./tpcds-kit
    - name: Build tpcds-kit
      if: steps.cache-tpcds-sf-1.outputs.cache-hit != 'true'
      run: cd tpcds-kit/tools && make OS=LINUX
    - name: Generate TPC-DS (SF=1) table data
      if: steps.cache-tpcds-sf-1.outputs.cache-hit != 'true'
      run: build/sbt "sql/Test/runMain org.apache.spark.sql.GenTPCDSData --dsdgenDir `pwd`/tpcds-kit/tools --location `pwd`/tpcds-sf-1 --scaleFactor 1 --numPartitions 1 --overwrite"
    - name: Run TPC-DS queries (Sort merge join)
      run: |
        SPARK_TPCDS_DATA=`pwd`/tpcds-sf-1 build/sbt "sql/testOnly org.apache.spark.sql.TPCDSQueryTestSuite"
      env:
        SPARK_ANSI_SQL_MODE: ${{ fromJSON(inputs.envs).SPARK_ANSI_SQL_MODE }}
        SPARK_TPCDS_JOIN_CONF: |
          spark.sql.autoBroadcastJoinThreshold=-1
          spark.sql.join.preferSortMergeJoin=true
    - name: Run TPC-DS queries (Broadcast hash join)
      run: |
        SPARK_TPCDS_DATA=`pwd`/tpcds-sf-1 build/sbt "sql/testOnly org.apache.spark.sql.TPCDSQueryTestSuite"
      env:
        SPARK_ANSI_SQL_MODE: ${{ fromJSON(inputs.envs).SPARK_ANSI_SQL_MODE }}
        SPARK_TPCDS_JOIN_CONF: |
          spark.sql.autoBroadcastJoinThreshold=10485760
    - name: Run TPC-DS queries (Shuffled hash join)
      run: |
        SPARK_TPCDS_DATA=`pwd`/tpcds-sf-1 build/sbt "sql/testOnly org.apache.spark.sql.TPCDSQueryTestSuite"
      env:
        SPARK_ANSI_SQL_MODE: ${{ fromJSON(inputs.envs).SPARK_ANSI_SQL_MODE }}
        SPARK_TPCDS_JOIN_CONF: |
          spark.sql.autoBroadcastJoinThreshold=-1
          spark.sql.join.forceApplyShuffledHashJoin=true
    - name: Upload test results to report
      if: always()
      uses: actions/upload-artifact@v3
      with:
        name: test-results-tpcds--8-${{ inputs.hadoop }}-hive2.3
        path: "**/target/test-reports/*.xml"
    - name: Upload unit tests log files
      if: failure()
      uses: actions/upload-artifact@v3
      with:
        name: unit-tests-log-tpcds--8-${{ inputs.hadoop }}-hive2.3
        path: "**/target/unit-tests.log"

  docker-integration-tests:
    needs: precondition
    if: fromJson(needs.precondition.outputs.required).docker-integration-tests == 'true'
    name: Run Docker integration tests
    runs-on: ubuntu-20.04
    env:
      HADOOP_PROFILE: ${{ inputs.hadoop }}
      HIVE_PROFILE: hive2.3
      GITHUB_PREV_SHA: ${{ github.event.before }}
      SPARK_LOCAL_IP: localhost
      ORACLE_DOCKER_IMAGE_NAME: gvenzl/oracle-xe:21.3.0
      SKIP_MIMA: true
      SKIP_PACKAGING: true
    steps:
    - name: Checkout Spark repository
      uses: actions/checkout@v3
      with:
        fetch-depth: 0
        repository: apache/spark
        ref: ${{ inputs.branch }}
    - name: Sync the current branch with the latest in Apache Spark
      if: github.repository != 'apache/spark'
      run: |
        echo "APACHE_SPARK_REF=$(git rev-parse HEAD)" >> $GITHUB_ENV
        git fetch https://github.com/$GITHUB_REPOSITORY.git ${GITHUB_REF#refs/heads/}
        git -c user.name='Apache Spark Test Account' -c user.email='<EMAIL>' merge --no-commit --progress --squash FETCH_HEAD
        git -c user.name='Apache Spark Test Account' -c user.email='<EMAIL>' commit -m "Merged commit" --allow-empty
    - name: Cache Scala, SBT and Maven
      uses: actions/cache@v3
      with:
        path: |
          build/apache-maven-*
          build/scala-*
          build/*.jar
          ~/.sbt
        key: build-${{ hashFiles('**/pom.xml', 'project/build.properties', 'build/mvn', 'build/sbt', 'build/sbt-launch-lib.bash', 'build/spark-build-info') }}
        restore-keys: |
          build-
    - name: Cache Coursier local repository
      uses: actions/cache@v3
      with:
        path: ~/.cache/coursier
        key: docker-integration-coursier-${{ hashFiles('**/pom.xml', '**/plugins.sbt') }}
        restore-keys: |
          docker-integration-coursier-
    - name: Install Java 8
      uses: actions/setup-java@v3
      with:
        distribution: temurin
        java-version: 8
    - name: Run tests
      run: |
        ./dev/run-tests --parallelism 1 --modules docker-integration-tests --included-tags org.apache.spark.tags.DockerTest
    - name: Upload test results to report
      if: always()
      uses: actions/upload-artifact@v3
      with:
        name: test-results-docker-integration--8-${{ inputs.hadoop }}-hive2.3
        path: "**/target/test-reports/*.xml"
    - name: Upload unit tests log files
      if: failure()
      uses: actions/upload-artifact@v3
      with:
        name: unit-tests-log-docker-integration--8-${{ inputs.hadoop }}-hive2.3
        path: "**/target/unit-tests.log"

  k8s-integration-tests:
    needs: precondition
    if: fromJson(needs.precondition.outputs.required).k8s-integration-tests == 'true'
    name: Run Spark on Kubernetes Integration test
    runs-on: ubuntu-20.04
    steps:
      - name: Checkout Spark repository
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
          repository: apache/spark
          ref: ${{ inputs.branch }}
      - name: Sync the current branch with the latest in Apache Spark
        if: github.repository != 'apache/spark'
        run: |
          echo "APACHE_SPARK_REF=$(git rev-parse HEAD)" >> $GITHUB_ENV
          git fetch https://github.com/$GITHUB_REPOSITORY.git ${GITHUB_REF#refs/heads/}
          git -c user.name='Apache Spark Test Account' -c user.email='<EMAIL>' merge --no-commit --progress --squash FETCH_HEAD
          git -c user.name='Apache Spark Test Account' -c user.email='<EMAIL>' commit -m "Merged commit" --allow-empty
      - name: Cache Scala, SBT and Maven
        uses: actions/cache@v3
        with:
          path: |
            build/apache-maven-*
            build/scala-*
            build/*.jar
            ~/.sbt
          key: build-${{ hashFiles('**/pom.xml', 'project/build.properties', 'build/mvn', 'build/sbt', 'build/sbt-launch-lib.bash', 'build/spark-build-info') }}
          restore-keys: |
            build-
      - name: Cache Coursier local repository
        uses: actions/cache@v3
        with:
          path: ~/.cache/coursier
          key: k8s-integration-coursier-${{ hashFiles('**/pom.xml', '**/plugins.sbt') }}
          restore-keys: |
            k8s-integration-coursier-
      - name: Install Java ${{ inputs.java }}
        uses: actions/setup-java@v3
        with:
          distribution: temurin
          java-version: ${{ inputs.java }}
      - name: start minikube
        run: |
          # See more in "Installation" https://minikube.sigs.k8s.io/docs/start/
          curl -LO https://storage.googleapis.com/minikube/releases/latest/minikube-linux-amd64
          sudo install minikube-linux-amd64 /usr/local/bin/minikube
          # Github Action limit cpu:2, memory: 6947MB, limit to 2U6G for better resource statistic
          minikube start --cpus 2 --memory 6144
      - name: Print K8S pods and nodes info
        run: |
          kubectl get pods -A
          kubectl describe node
      - name: Run Spark on K8S integration test (With driver cpu 0.5, executor cpu 0.2 limited)
        run: |
          # Prepare PV test
          PVC_TMP_DIR=$(mktemp -d)
          export PVC_TESTS_HOST_PATH=$PVC_TMP_DIR
          export PVC_TESTS_VM_PATH=$PVC_TMP_DIR
          minikube mount ${PVC_TESTS_HOST_PATH}:${PVC_TESTS_VM_PATH} --gid=0 --uid=185 &
          kubectl create clusterrolebinding serviceaccounts-cluster-admin --clusterrole=cluster-admin --group=system:serviceaccounts || true
          kubectl apply -f https://raw.githubusercontent.com/volcano-sh/volcano/v1.7.0/installer/volcano-development.yaml || true
          eval $(minikube docker-env)
          build/sbt -Psparkr -Pkubernetes -Pvolcano -Pkubernetes-integration-tests -Dspark.kubernetes.test.volcanoMaxConcurrencyJobNum=1 -Dtest.exclude.tags=local "kubernetes-integration-tests/test"
      - name: Upload Spark on K8S integration tests log files
        if: failure()
        uses: actions/upload-artifact@v3
        with:
          name: spark-on-kubernetes-it-log
          path: "**/target/integration-tests.log"
