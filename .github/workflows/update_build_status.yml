#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

name: Update build status workflow

on:
  schedule:
  - cron: "*/15 * * * *"

jobs:
  update:
    name: Update build status
    runs-on: ubuntu-20.04
    permissions:
      actions: read
      checks: write
    steps:
      - name: "Update build status"
        uses: actions/github-script@v6
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const endpoint = 'GET /repos/:owner/:repo/pulls?state=:state'
            const params = {
              owner: context.repo.owner,
              repo: context.repo.repo,
              state: 'open'
            }

            // See https://docs.github.com/en/graphql/reference/enums#mergestatestatus
            const maybeReady = ['behind', 'clean', 'draft', 'has_hooks', 'unknown', 'unstable'];

            // Iterate open PRs
            for await (const prs of github.paginate.iterator(endpoint,params)) {
              // Each page
              for await (const pr of prs.data) {
                console.log('SHA: ' + pr.head.sha)
                console.log('  Mergeable status: ' + pr.mergeable_state)
                if (pr.mergeable_state == null || maybeReady.includes(pr.mergeable_state)) {
                  const checkRuns = await github.request('GET /repos/{owner}/{repo}/commits/{ref}/check-runs', {
                    owner: context.repo.owner,
                    repo: context.repo.repo,
                    ref: pr.head.sha
                  })

                  // Iterator GitHub Checks in the PR
                  for await (const cr of checkRuns.data.check_runs) {
                    if (cr.name == 'Build' && cr.conclusion != "action_required") {
                      // text contains parameters to make request in JSON.
                      const params = JSON.parse(cr.output.text)

                      // Get the workflow run in the forked repository
                      let run
                      try {
                        run = await github.request('GET /repos/{owner}/{repo}/actions/runs/{run_id}', params)
                      } catch (error) {
                        console.error(error)
                        // Run not found. This can happen when the PR author removes GitHub Actions runs or
                        // disalbes GitHub Actions.
                        continue
                      }

                      // Keep syncing the status of the checks
                      if (run.data.status == 'completed') {
                        console.log('    Run ' + cr.id + ': set status (' + run.data.status + ') and conclusion (' + run.data.conclusion + ')')
                        const response = await github.request('PATCH /repos/{owner}/{repo}/check-runs/{check_run_id}', {
                          owner: context.repo.owner,
                          repo: context.repo.repo,
                          check_run_id: cr.id,
                          output: cr.output,
                          status: run.data.status,
                          conclusion: run.data.conclusion,
                          details_url: run.data.details_url
                        })
                      } else {
                        console.log('    Run ' + cr.id + ': set status (' + run.data.status + ')')
                        const response = await github.request('PATCH /repos/{owner}/{repo}/check-runs/{check_run_id}', {
                          owner: context.repo.owner,
                          repo: context.repo.repo,
                          check_run_id: cr.id,
                          output: cr.output,
                          status: run.data.status,
                          details_url: run.data.details_url
                        })
                      }

                      break
                    }
                  }
                }
              }
            }
