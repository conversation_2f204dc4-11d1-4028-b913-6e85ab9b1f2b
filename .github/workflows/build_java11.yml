#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

name: "Build (master, Scala 2.12, Hadoop 3, JDK 11)"

on:
  schedule:
    - cron: '0 16 * * *'

jobs:
  run-build:
    permissions:
      packages: write
    name: Run
    uses: ./.github/workflows/build_and_test.yml
    if: github.repository == 'apache/spark'
    with:
      java: 11
      branch: master
      hadoop: hadoop3
      envs: >-
        {
          "SKIP_MIMA": "true",
          "SKIP_UNIDOC": "true"
        }
      jobs: >-
        {
          "build": "true",
          "pyspark": "true",
          "sparkr": "true",
          "tpcds-1g": "true",
          "docker-integration-tests": "true"
        }
