#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

name: Report test results
on:
  workflow_run:
    workflows: ["Build"]
    types:
      - completed

jobs:
  test_report:
    if: github.event.workflow_run.conclusion != 'skipped'
    runs-on: ubuntu-latest
    steps:
    - name: Download test results to report
      uses: dawidd6/action-download-artifact@09385b76de790122f4da9c82b17bccf858b9557c # pin@v2
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        workflow: ${{ github.event.workflow_run.workflow_id }}
        commit: ${{ github.event.workflow_run.head_commit.id }}
        workflow_conclusion: completed
    - name: Publish test report
      uses: scacap/action-surefire-report@482f012643ed0560e23ef605a79e8e87ca081648 # pin@v1
      with:
        check_name: Report test results
        github_token: ${{ secrets.GITHUB_TOKEN }}
        report_paths: "**/target/test-reports/*.xml"
        commit: ${{ github.event.workflow_run.head_commit.id }}
