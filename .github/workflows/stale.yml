#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

name: Close stale PRs

on:
  schedule:
  - cron: "0 0 * * *"

jobs:
  stale:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/stale@c201d45ef4b0ccbd3bb0616f93bae13e73d0a080 # pin@v1.1.0
      with:
        repo-token: ${{ secrets.GITHUB_TOKEN }}
        stale-pr-message: >
          We're closing this PR because it hasn't been updated in a while.
          This isn't a judgement on the merit of the PR in any way. It's just
          a way of keeping the PR queue manageable.

          If you'd like to revive this PR, please reopen it and ask a
          committer to remove the Stale tag!
        days-before-stale: 100
        # Setting this to 0 is the same as setting it to 1.
        # See: https://github.com/actions/stale/issues/28
        days-before-close: 0
