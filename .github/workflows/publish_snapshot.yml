#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

name: Publish Snapshot

on:
  schedule:
  - cron: '0 0 * * *'

jobs:
  publish-snapshot:
    if: github.repository == 'apache/spark'
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        branch:
          - master
          - branch-3.3
          - branch-3.2
    steps:
    - name: Checkout Spark repository
      uses: actions/checkout@v3
      with:
        ref: ${{ matrix.branch }}
    - name: Cache Maven local repository
      uses: actions/cache@v3
      with:
        path: ~/.m2/repository
        key: snapshot-maven-${{ hashFiles('**/pom.xml') }}
        restore-keys: |
          snapshot-maven-
    - name: Install Java 8
      uses: actions/setup-java@v3
      with:
        distribution: temurin
        java-version: 8
    - name: Publish snapshot
      env:
        ASF_USERNAME: ${{ secrets.NEXUS_USER }}
        ASF_PASSWORD: ${{ secrets.NEXUS_PW }}
        GPG_KEY: "not_used"
        GPG_PASSPHRASE: "not_used"
        GIT_REF: ${{ matrix.branch }}
      run: ./dev/create-release/release-build.sh publish-snapshot
