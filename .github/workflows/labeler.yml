#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

# Intentionally has a general name.
# because the test status check created in GitHub Actions
# currently randomly picks any associated workflow.
# So, the name was changed to make sense in that context too.
# See also https://github.community/t/specify-check-suite-when-creating-a-checkrun/118380/10

name: "On pull requests"
on: pull_request_target

jobs:
  label:
    name: Label pull requests
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: write
    steps:
    # In order to get back the negated matches like in the old config,
    # we need the actinons/labeler concept of `all` and `any` which matches
    # all of the given constraints / glob patterns for either `all`
    # files or `any` file in the change set.
    #
    # Github issue which requests a timeline for a release with any/all support:
    #     - https://github.com/actions/labeler/issues/111
    # This issue also references the issue that mentioned that any/all are only
    # supported on main branch (previously called master):
    #    - https://github.com/actions/labeler/issues/73#issuecomment-639034278
    #
    # However, these are not in a published release and the current `main` branch
    # has some issues upon testing.
    - uses: actions/labeler@v4
      with:
        repo-token: "${{ secrets.GITHUB_TOKEN }}"
        sync-labels: true
