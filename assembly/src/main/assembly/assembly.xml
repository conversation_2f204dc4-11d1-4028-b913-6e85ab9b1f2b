<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one or more
  ~ contributor license agreements.  See the NOTICE file distributed with
  ~ this work for additional information regarding copyright ownership.
  ~ The ASF licenses this file to You under the Apache License, Version 2.0
  ~ (the "License"); you may not use this file except in compliance with
  ~ the License.  You may obtain a copy of the License at
  ~
  ~    http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<assembly>
  <id>dist</id>
  <formats>
    <format>tar.gz</format>
    <format>dir</format>
  </formats>
  <includeBaseDirectory>false</includeBaseDirectory>

  <fileSets>
    <fileSet>
      <includes>
        <include>README</include>
      </includes>
    </fileSet>
    <fileSet>
      <directory>
        ${project.parent.basedir}/core/src/main/resources/org/apache/spark/ui/static/
      </directory>
      <outputDirectory>ui-resources/org/apache/spark/ui/static</outputDirectory>
      <includes>
        <include>**/*</include>
      </includes>
    </fileSet>
    <fileSet>
      <directory>
        ${project.parent.basedir}/sbin/
      </directory>
      <outputDirectory>sbin</outputDirectory>
      <includes>
        <include>**/*</include>
      </includes>
    </fileSet>
    <fileSet>
      <directory>
        ${project.parent.basedir}/bin/
      </directory>
      <outputDirectory>bin</outputDirectory>
      <includes>
        <include>**/*</include>
      </includes>
    </fileSet>
    <fileSet>
      <directory>
        ${project.parent.basedir}/assembly/target/${spark.jar.dir}
      </directory>
      <outputDirectory></outputDirectory>
      <includes>
        <include>${spark.jar.basename}</include>
      </includes>
    </fileSet>
  </fileSets>

  <dependencySets>
    <dependencySet>
      <includes>
        <include>org.apache.spark:*:jar</include>
      </includes>
      <excludes>
        <exclude>org.apache.spark:spark-assembly:jar</exclude>
      </excludes>
    </dependencySet>
    <dependencySet>
      <outputDirectory>lib</outputDirectory>
      <useTransitiveDependencies>true</useTransitiveDependencies>
      <unpack>false</unpack>
      <scope>runtime</scope>
      <useProjectArtifact>false</useProjectArtifact>
      <excludes>
        <exclude>org.apache.hadoop:*:jar</exclude>
        <exclude>org.apache.spark:*:jar</exclude>
        <exclude>org.apache.zookeeper:*:jar</exclude>
        <exclude>org.apache.avro:*:jar</exclude>
      </excludes>
    </dependencySet>
  </dependencySets>

</assembly>
