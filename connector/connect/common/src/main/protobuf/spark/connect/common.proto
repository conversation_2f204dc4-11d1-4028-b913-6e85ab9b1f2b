/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

syntax = 'proto3';

package spark.connect;

option java_multiple_files = true;
option java_package = "org.apache.spark.connect.proto";

// StorageLevel for persisting Datasets/Tables.
message StorageLevel {
  // (Required) Whether the cache should use disk or not.
  bool use_disk = 1;
  // (Required) Whether the cache should use memory or not.
  bool use_memory = 2;
  // (Required) Whether the cache should use off-heap or not.
  bool use_off_heap = 3;
  // (Required) Whether the cached data is deserialized or not.
  bool deserialized = 4;
  // (Required) The number of replicas.
  int32 replication = 5;
}
