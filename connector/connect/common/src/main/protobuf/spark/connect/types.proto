/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

syntax = 'proto3';

package spark.connect;

option java_multiple_files = true;
option java_package = "org.apache.spark.connect.proto";

// This message describes the logical [[DataType]] of something. It does not carry the value
// itself but only describes it.
message DataType {
  oneof kind {
    NULL null = 1;

    Binary binary = 2;

    Boolean boolean = 3;

    // Numeric types
    Byte byte = 4;
    Short short = 5;
    Integer integer = 6;
    Long long = 7;

    Float float = 8;
    Double double = 9;
    Decimal decimal = 10;

    // String types
    String string = 11;
    Char char = 12;
    VarChar var_char = 13;

    // Datatime types
    Date date = 14;
    Timestamp timestamp = 15;
    TimestampNTZ timestamp_ntz = 16;

    // Interval types
    CalendarInterval calendar_interval = 17;
    YearMonthInterval year_month_interval = 18;
    DayTimeInterval day_time_interval = 19;

    // Complex types
    Array array = 20;
    Struct struct = 21;
    Map map = 22;

    // UserDefinedType
    UDT udt = 23;

    // UnparsedDataType
    Unparsed unparsed = 24;
  }

  message Boolean {
    uint32 type_variation_reference = 1;
  }

  message Byte {
    uint32 type_variation_reference = 1;
  }

  message Short {
    uint32 type_variation_reference = 1;
  }

  message Integer {
    uint32 type_variation_reference = 1;
  }

  message Long {
    uint32 type_variation_reference = 1;
  }

  message Float {
    uint32 type_variation_reference = 1;
  }

  message Double {
    uint32 type_variation_reference = 1;
  }

  message String {
    uint32 type_variation_reference = 1;
  }

  message Binary {
    uint32 type_variation_reference = 1;
  }

  message NULL {
    uint32 type_variation_reference = 1;
  }

  message Timestamp {
    uint32 type_variation_reference = 1;
  }

  message Date {
    uint32 type_variation_reference = 1;
  }

  message TimestampNTZ {
    uint32 type_variation_reference = 1;
  }

  message CalendarInterval {
    uint32 type_variation_reference = 1;
  }

  message YearMonthInterval {
    optional int32 start_field = 1;
    optional int32 end_field = 2;
    uint32 type_variation_reference = 3;
  }

  message DayTimeInterval {
    optional int32 start_field = 1;
    optional int32 end_field = 2;
    uint32 type_variation_reference = 3;
  }

  // Start compound types.
  message Char {
    int32 length = 1;
    uint32 type_variation_reference = 2;
  }

  message VarChar {
    int32 length = 1;
    uint32 type_variation_reference = 2;
  }

  message Decimal {
    optional int32 scale = 1;
    optional int32 precision = 2;
    uint32 type_variation_reference = 3;
  }

  message StructField {
    string name = 1;
    DataType data_type = 2;
    bool nullable = 3;
    optional string metadata = 4;
  }

  message Struct {
    repeated StructField fields = 1;
    uint32 type_variation_reference = 2;
  }

  message Array {
    DataType element_type = 1;
    bool contains_null = 2;
    uint32 type_variation_reference = 3;
  }

  message Map {
    DataType key_type = 1;
    DataType value_type = 2;
    bool value_contains_null = 3;
    uint32 type_variation_reference = 4;
  }

  message UDT {
    string type = 1;
    optional string jvm_class = 2;
    optional string python_class = 3;
    optional string serialized_python_class = 4;
    DataType sql_type = 5;
  }

  message Unparsed {
    // (Required) The unparsed data type string
    string data_type_string = 1;
  }
}
