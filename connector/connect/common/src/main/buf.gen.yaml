#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
version: v1
plugins:
  - remote: buf.build/protocolbuffers/plugins/cpp:v3.20.0-1
    out: gen/proto/cpp
  - remote: buf.build/protocolbuffers/plugins/csharp:v3.20.0-1
    out: gen/proto/csharp
  - remote: buf.build/protocolbuffers/plugins/java:v3.20.0-1
    out: gen/proto/java
  - remote: buf.build/grpc/plugins/ruby:v1.47.0-1
    out: gen/proto/ruby
  - remote: buf.build/protocolbuffers/plugins/ruby:v21.2.0-1
    out: gen/proto/ruby
   # Building the Python build and building the mypy interfaces.
  - remote: buf.build/protocolbuffers/plugins/python:v3.19.3-1
    out: gen/proto/python
  - remote: buf.build/grpc/plugins/python:v1.47.0-1
    out: gen/proto/python
  - name: mypy
    out: gen/proto/python

