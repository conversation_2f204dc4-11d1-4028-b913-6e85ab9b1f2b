<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one or more
  ~ contributor license agreements.  See the NOTICE file distributed with
  ~ this work for additional information regarding copyright ownership.
  ~ The ASF licenses this file to You under the Apache License, Version 2.0
  ~ (the "License"); you may not use this file except in compliance with
  ~ the License.  You may obtain a copy of the License at
  ~
  ~    http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.apache.spark</groupId>
    <artifactId>spark-parent_2.12</artifactId>
    <version>3.4.4</version>
    <relativePath>../../../../pom.xml</relativePath>
  </parent>

  <artifactId>spark-connect-client-jvm_2.12</artifactId>
  <packaging>jar</packaging>
  <name>Spark Project Connect Client</name>
  <url>https://spark.apache.org/</url>
  <properties>
    <sbt.project.name>connect-client-jvm</sbt.project.name>
    <guava.version>31.0.1-jre</guava.version>
    <guava.failureaccess.version>1.0.1</guava.failureaccess.version>
    <mima.version>1.1.0</mima.version>
  </properties>

  <dependencies>
    <dependency>
      <groupId>org.apache.spark</groupId>
      <artifactId>spark-connect-common_${scala.binary.version}</artifactId>
      <version>${project.version}</version>
      <exclusions>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>guava</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <!--TODO: fix the dependency once the catalyst refactoring is done-->
    <dependency>
      <groupId>org.apache.spark</groupId>
      <artifactId>spark-catalyst_${scala.binary.version}</artifactId>
      <version>${project.version}</version>
      <scope>provided</scope>
      <exclusions>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>guava</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.google.protobuf</groupId>
      <artifactId>protobuf-java</artifactId>
      <version>${protobuf.version}</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
      <version>${guava.version}</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>failureaccess</artifactId>
      <version>${guava.failureaccess.version}</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>io.netty</groupId>
      <artifactId>netty-codec-http2</artifactId>
      <version>${netty.version}</version>
    </dependency>
    <dependency>
      <groupId>io.netty</groupId>
      <artifactId>netty-handler-proxy</artifactId>
      <version>${netty.version}</version>
    </dependency>
    <dependency>
      <groupId>io.netty</groupId>
      <artifactId>netty-transport-native-unix-common</artifactId>
      <version>${netty.version}</version>
    </dependency>
    <dependency>
      <groupId>com.lihaoyi</groupId>
      <artifactId>ammonite_${scala.version}</artifactId>
      <version>${ammonite.version}</version>
      <scope>provided</scope>
      <exclusions>
        <exclusion>
          <groupId>org.scala-lang.modules</groupId>
          <artifactId>scala-xml_${scala.binary.version}</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.apache.spark</groupId>
      <artifactId>spark-connect-common_${scala.binary.version}</artifactId>
      <version>${project.version}</version>
      <type>test-jar</type>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>guava</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.scalacheck</groupId>
      <artifactId>scalacheck_${scala.binary.version}</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <scope>test</scope>
    </dependency>
    <!-- Use mima to perform the compatibility check -->
    <dependency>
      <groupId>com.typesafe</groupId>
      <artifactId>mima-core_${scala.binary.version}</artifactId>
      <version>${mima.version}</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
  <build>
    <testOutputDirectory>target/scala-${scala.binary.version}/test-classes</testOutputDirectory>
    <plugins>
      <!-- Shade all Guava / Protobuf / Netty dependencies of this build -->
      <!-- TODO (SPARK-42449): Ensure shading rules are handled correctly in `native-image.properties` and support GraalVM   -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-shade-plugin</artifactId>
        <configuration>
          <shadedArtifactAttached>false</shadedArtifactAttached>
          <artifactSet>
            <includes>
              <include>com.google.android:*</include>
              <include>com.google.api.grpc:*</include>
              <include>com.google.code.findbugs:*</include>
              <include>com.google.code.gson:*</include>
              <include>com.google.errorprone:*</include>
              <include>com.google.guava:*</include>
              <include>com.google.j2objc:*</include>
              <include>com.google.protobuf:*</include>
              <include>io.grpc:*</include>
              <include>io.netty:*</include>
              <include>io.perfmark:*</include>
              <include>org.codehaus.mojo:*</include>
              <include>org.checkerframework:*</include>
              <include>org.apache.spark:spark-connect-common_${scala.binary.version}</include>
            </includes>
          </artifactSet>
          <relocations>
            <relocation>
              <pattern>io.grpc</pattern>
              <shadedPattern>${spark.shade.packageName}.connect.client.io.grpc</shadedPattern>
              <includes>
                <include>io.grpc.**</include>
              </includes>
            </relocation>
            <relocation>
              <pattern>com.google</pattern>
              <shadedPattern>${spark.shade.packageName}.connect.client.com.google</shadedPattern>
            </relocation>
            <relocation>
              <pattern>io.netty</pattern>
              <shadedPattern>${spark.shade.packageName}.connect.client.io.netty</shadedPattern>
            </relocation>
            <relocation>
              <pattern>org.checkerframework</pattern>
              <shadedPattern>${spark.shade.packageName}.connect.client.org.checkerframework</shadedPattern>
            </relocation>
            <relocation>
              <pattern>javax.annotation</pattern>
              <shadedPattern>${spark.shade.packageName}.connect.client.javax.annotation</shadedPattern>
            </relocation>
            <relocation>
              <pattern>io.perfmark</pattern>
              <shadedPattern>${spark.shade.packageName}.connect.client.io.perfmark</shadedPattern>
            </relocation>
            <relocation>
              <pattern>org.codehaus</pattern>
              <shadedPattern>${spark.shade.packageName}.connect.client.org.codehaus</shadedPattern>
            </relocation>
            <relocation>
              <pattern>android.annotation</pattern>
              <shadedPattern>${spark.shade.packageName}.connect.client.android.annotation</shadedPattern>
            </relocation>
          </relocations>
          <!--SPARK-42228: Add `ServicesResourceTransformer` to relocation class names in META-INF/services for grpc-->
          <transformers>
            <transformer implementation="org.apache.maven.plugins.shade.resource.ServicesResourceTransformer"/>
          </transformers>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <executions>
          <execution>
            <id>prepare-test-jar</id>
            <phase>test-compile</phase>
            <goals>
              <goal>test-jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>