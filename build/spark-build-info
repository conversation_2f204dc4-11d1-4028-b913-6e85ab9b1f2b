#!/usr/bin/env bash

#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

# This script generates the build info for spark and places it into the spark-version-info.properties file.
# Arguments:
#   build_tgt_directory - The target directory where properties file would be created. [./core/target/extra-resources]
#   spark_version - The current version of spark

RESOURCE_DIR="$1"
mkdir -p "$RESOURCE_DIR"
SPARK_BUILD_INFO="${RESOURCE_DIR%/}"/spark-version-info.properties

echo_build_properties() {
  # $1:spark version
  #    eg. spark-3.4.4-SNAPSHOT
  # $TAG:(online)-(branch name/tag name)-(current time)
  #     eg. online-master-20240808-170201
  echo version=$1.$TAG
  echo user=$USER
  echo revision=$(git rev-parse HEAD)
  echo branch=$(git rev-parse --abbrev-ref HEAD)
  echo date=$(date -u +%Y-%m-%dT%H:%M:%SZ)
  echo url=$(git config --get remote.origin.url |  sed 's|https://\(.*\)@\(.*\)|https://\2|')
  echo docroot=https://spark.apache.org/docs/latest
}

echo_build_properties $2 > "$SPARK_BUILD_INFO"
