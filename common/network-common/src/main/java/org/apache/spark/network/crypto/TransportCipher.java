/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.spark.network.crypto;

import com.google.common.annotations.VisibleForTesting;
import com.google.crypto.tink.subtle.Hex;
import com.google.crypto.tink.subtle.Hkdf;
import io.netty.channel.Channel;

import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.GeneralSecurityException;

interface TransportCipher {
    String getKeyId() throws GeneralSecurityException;
    void addToChannel(Channel channel) throws IOException, GeneralSecurityException;
}

class TransportCipherUtil {
    /*
     * This method is used for testing to verify key derivation.
     */
    @VisibleForTesting
    static String getKeyId(SecretKeySpec key) throws GeneralSecurityException {
        byte[] keyIdBytes = Hkdf.computeHkdf("HmacSha256",
                key.getEncoded(),
                null,
                "keyID".getBytes(StandardCharsets.UTF_8),
                32);
        return Hex.encode(keyIdBytes);
    }
}
