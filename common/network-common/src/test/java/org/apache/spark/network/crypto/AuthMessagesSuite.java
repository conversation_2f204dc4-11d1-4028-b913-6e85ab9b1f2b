/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.spark.network.crypto;

import java.util.Arrays;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertArrayEquals;
import org.junit.Test;

public class AuthMessagesSuite {

  private static int COUNTER = 0;

  private static String string() {
    return String.valueOf(COUNTER++);
  }

  private static byte[] byteArray() {
    byte[] bytes = new byte[COUNTER++];
    Arrays.fill(bytes, (byte) COUNTER);
    return bytes;
  }

  @Test
  public void testPublicKeyEncodeDecode() {
    AuthMessage msg = new AuthMessage(string(), byteArray(), byteArray());
    ByteBuf buf = Unpooled.buffer();
    msg.encode(buf);
    AuthMessage decoded = AuthMessage.decodeMessage(buf.nioBuffer());

    assertEquals(msg.appId, decoded.appId);
    assertArrayEquals(msg.salt, decoded.salt);
    assertArrayEquals(msg.ciphertext, decoded.ciphertext);
  }
}
