/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.spark.network.util;

import org.apache.hadoop.hdfs.JDDFSConfigKeys;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;

import java.util.HashMap;
import java.util.Map;

public class TransportConfSuite {

    @Test
    public void testTransportConfQosEnabledHdfsQosEnabled() {
        ConfigProvider configProvider = Mockito.mock(ConfigProvider.class);
        Mockito.when(configProvider.getBoolean(Mockito.anyString(), Mockito.anyBoolean())).thenReturn(true);
        Mockito.when(configProvider.getBoolean("spark.hadoop." + JDDFSConfigKeys.DFS_PIPELINE_SUPPORT_QOS,
                JDDFSConfigKeys.DFS_PIPELINE_SUPPORT_QOS_DEFAULT)).thenReturn(true);
        Mockito.when(configProvider.getInt("spark.hadoop." + JDDFSConfigKeys.DFS_PIPELINE_QOS_VALUE_KEY,
                JDDFSConfigKeys.DFS_PIPELINE_QOS_VALUE_DEFAULT)).thenReturn(100);
        Mockito.when(configProvider.getInt("spark.network.qos.value",
                JDDFSConfigKeys.DFS_PIPELINE_QOS_VALUE_DEFAULT)).thenReturn(50);

        TransportConf transportConf = new TransportConf("testModule", configProvider);

        // Assert qosValue is set to 100
        Assert.assertEquals(100, transportConf.qosValue);
    }

    @Test
    public void testTransportConfQosEnabledHdfsQosDisabled() {
        ConfigProvider configProvider = Mockito.mock(ConfigProvider.class);
        Mockito.when(configProvider.getBoolean(Mockito.anyString(), Mockito.anyBoolean())).thenReturn(true);
        Mockito.when(configProvider.getBoolean("spark.hadoop." + JDDFSConfigKeys.DFS_PIPELINE_SUPPORT_QOS,
                JDDFSConfigKeys.DFS_PIPELINE_SUPPORT_QOS_DEFAULT)).thenReturn(false);
        Mockito.when(configProvider.getInt("spark.network.qos.value",
                JDDFSConfigKeys.DFS_PIPELINE_QOS_VALUE_DEFAULT)).thenReturn(50);
        Mockito.when(configProvider.getInt("spark.hadoop." + JDDFSConfigKeys.DFS_PIPELINE_QOS_VALUE_KEY,
                JDDFSConfigKeys.DFS_PIPELINE_QOS_VALUE_DEFAULT)).thenReturn(100);
        TransportConf transportConf = new TransportConf("testModule", configProvider);

        // Assert qosValue is set to 50
        Assert.assertEquals(50, transportConf.qosValue);
    }

    @Test
    public void testTransportConfQosDisabled() {
        ConfigProvider configProvider = Mockito.mock(ConfigProvider.class);
        Mockito.when(configProvider.getBoolean(Mockito.anyString(), Mockito.anyBoolean())).thenReturn(false);

        TransportConf transportConf = new TransportConf("testModule", configProvider);

        // Assert qosValue is set to 0
        Assert.assertEquals(0, transportConf.qosValue);
    }

    @Test
    public void testTransportConfExceptionHandling() {
        ConfigProvider configProvider = Mockito.mock(ConfigProvider.class);
        Mockito.when(configProvider.getBoolean(Mockito.anyString(), Mockito.anyBoolean())).thenReturn(true);
        Mockito.when(configProvider.getBoolean("spark.hadoop." + JDDFSConfigKeys.DFS_PIPELINE_SUPPORT_QOS,
                JDDFSConfigKeys.DFS_PIPELINE_SUPPORT_QOS_DEFAULT)).thenReturn(true);
        Mockito.when(configProvider.getInt("spark.hadoop." + JDDFSConfigKeys.DFS_PIPELINE_QOS_VALUE_KEY,
                JDDFSConfigKeys.DFS_PIPELINE_QOS_VALUE_DEFAULT)).thenThrow(new RuntimeException("Test Exception"));

        TransportConf transportConf = new TransportConf("testModule", configProvider);

        // Assert qosValue is set to 0 due to exception
        Assert.assertEquals(0, transportConf.qosValue);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testTransportConfInvalidQosValue() {
        ConfigProvider configProvider = Mockito.mock(ConfigProvider.class);
        Mockito.when(configProvider.getBoolean(Mockito.anyString(), Mockito.anyBoolean())).thenReturn(true);
        Mockito.when(configProvider.getBoolean("spark.hadoop." + JDDFSConfigKeys.DFS_PIPELINE_SUPPORT_QOS,
                JDDFSConfigKeys.DFS_PIPELINE_SUPPORT_QOS_DEFAULT)).thenReturn(true);
        Mockito.when(configProvider.getInt("spark.hadoop." + JDDFSConfigKeys.DFS_PIPELINE_QOS_VALUE_KEY,
                JDDFSConfigKeys.DFS_PIPELINE_QOS_VALUE_DEFAULT)).thenReturn(300);

        TransportConf transportConf = new TransportConf("testModule", configProvider);
    }

    @Test
    public void testClientConnectionTimeoutMs() {
        TransportConf transportConf = new TransportConf("testModule", MapConfigProvider.EMPTY);
        Assert.assertEquals(transportConf.clientConnectionTimeoutMs(), 120000);
        Map map = new HashMap();
        map.put("spark.testModule.io.clientConnectionTimeout", "200s");
        transportConf = new TransportConf("testModule", new MapConfigProvider(map));
        Assert.assertEquals(transportConf.clientConnectionTimeoutMs(), 200000);
    }
}
