/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.spark.network.shuffle;

import java.util.EventListener;

import org.apache.spark.network.buffer.ManagedBuffer;

/**
 * This interface unifies both {@link BlockFetchingListener} and {@link BlockPushingListener}
 * under a single interface to allow code reuse, while also keeping the existing public interface
 * to facilitate backward compatibility.
 */
public interface BlockTransferListener extends EventListener {
  /**
   * Called once per successfully transferred block.
   */
  void onBlockTransferSuccess(String blockId, ManagedBuffer data);

  /**
   * Called at least once per block transfer failures.
   */
  void onBlockTransferFailure(String blockId, Throwable exception);

  /**
   * Return a string indicating the type of the listener such as fetch, push, or something else
   */
  String getTransferType();
}
